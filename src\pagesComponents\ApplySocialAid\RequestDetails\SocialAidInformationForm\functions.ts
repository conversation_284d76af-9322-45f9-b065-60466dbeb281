import * as Yup from "yup";
import { FormikHelpers } from "formik/dist/types";
import {
	ABANDONED,
	BORN_UNKNOWN_PARENTS,
	CHILD_IN_DIFFICULT_SITUATION,
	CHILD_OF_PRISONER,
	DIVORCED,
	EMIRATES_ID_REGEX,
	ENROLLED_IN_REHAB_CENTER,
	ORPHANS_ONLY,
	RELEASE_PRISONER,
	SPOUSE_INCAPACITATED_FOREIGNER,
	UNDER_45_AGE,
} from "config";

// Add constants for the category and subcategory IDs
const POD_CATEGORY_ID = "11a6a54f-9257-ee11-be6f-6045bd6aa1f5";
const POD_CHILD_SUBCATEGORY_ID = "093eb27d-9257-ee11-be6f-6045bd14ccdc";
const DIVORCED_SUBCATEGORY_ID = "0b3eb27d-9257-ee11-be6f-6045bd14ccdc";
const WOMAN_DIFFICULT_CATEGORY_ID = "13a6a54f-9257-ee11-be6f-6045bd6aa1f5";
const ABANDONED_SUBCATEGORY_ID = "143eb27d-9257-ee11-be6f-6045bd14ccdc";
const CHILD_DIFFICULT_SITUATION_ID = "15a6a54f-9257-ee11-be6f-6045bd6aa1f5";
const CHILD_UNKNOWN_PARENTAGE_ID = "1c3eb27d-9257-ee11-be6f-6045bd14ccdc";

// Add constants for marital status IDs
const DIVORCED_MARITAL_STATUS_ID = "3a422b1b-6d75-ed11-81ad-0022480dc264";
const WIDOWED_MARITAL_STATUS_ID = "3c422b1b-6d75-ed11-81ad-0022480dc264";
const GENDER_STATUS = "Male";

// Add a helper function to check eligibility
export const checkTopupEligibility = (
	category: any,
	subCategory: any,
	maritalStatusValue?: any,
	genderStatusValue?: any
) => {
	// Check if the applicant falls under any of the ineligible categories
	if (
		(category === POD_CATEGORY_ID && subCategory === POD_CHILD_SUBCATEGORY_ID) ||
		(subCategory === DIVORCED_SUBCATEGORY_ID && genderStatusValue != GENDER_STATUS) ||
		(category === WOMAN_DIFFICULT_CATEGORY_ID && subCategory === ABANDONED_SUBCATEGORY_ID) ||
		(category === CHILD_DIFFICULT_SITUATION_ID && subCategory === CHILD_UNKNOWN_PARENTAGE_ID)
	) {
		return false;
	}

	// Check if the applicant has an ineligible marital status
	if (maritalStatusValue === DIVORCED_MARITAL_STATUS_ID && genderStatusValue != GENDER_STATUS) {
		return false;
	}

	return true;
};

const getInitialValues = {
	// Social Aid Information
	//MaritalStatus: "",
	//Educations: "",
	//accomadations: "",
	ChildEligibilityforWomeninDifficulty: [],
	Category: "",
	SubCategory: "",
	IsActiveStudent: "",
	//HaveChildrenCustody: "",
	MilitaryServiceStatus: {},
	ReceivedLocalSupport: "",
	//PursuingHigherEducation: "",
	//PursuingMilitaryService: "",
	GuardianEmiratesID: "",
	NumberOfChildren: "",
	NumberOfChildrenLessThan25: "",
	Terminated: {},
	DateOfTermination: "",
	// Release Prisoner specific fields
	DateOfRelease: "",
	PrisonEntryDate: "",
	MilitaryServiceStatusForReleasePrisoner: {},
	// Rehabilitation Center specific fields
	RehabCenter: {},
	TreatmentPlanStartDate: "",
	TreatmentPlanEndDate: "",
};

const getValidationSchema = (t, age) => {
	//const { t } = useTranslation(["forms"]);
	return Yup.object({
		// MaritalStatus: Yup.object()
		// 	.shape({
		// 		label: Yup.string(),
		// 		value: Yup.string().required().label("thisField"),
		// 	})
		// 	.required()
		// 	.typeError("MaritalStatus"),
		// accomadations: Yup.object()
		// 	.shape({
		// 		label: Yup.string(),
		// 		value: Yup.string().required().label("thisField"),
		// 	})
		// 	.required()
		// 	.typeError("accomadations"),
		Category: Yup.object()
			.shape({
				label: Yup.string(),
				value: Yup.string().required().label("thisField"),
			})
			.required(),
		SubCategory: Yup.object()
			.shape({
				label: Yup.string(),
				value: Yup.string().required().label("thisField"),
			})
			.required(),
		ChildEligibilityforWomeninDifficulty: Yup.array().when("SubCategory", {
			is: (SubCategory) => {
				return (
					SubCategory?.value === DIVORCED ||
					SubCategory?.value === SPOUSE_INCAPACITATED_FOREIGNER ||
					SubCategory?.value === ABANDONED
				);
			},
			then: Yup.array().required().label("thisField").min(1),
			otherwise: Yup.array().notRequired(),
		}),
		IsActiveStudent: Yup.string().when("SubCategory", {
			is: (SubCategory) => {
				return SubCategory?.value === UNDER_45_AGE;
			},
			then: Yup.string().required().label("thisField").nullable(),
			otherwise: Yup.string().notRequired().nullable(),
		}),
		PursuingHigherEducation: Yup.string().when(["Category", "SubCategory"], {
			is: (Category: any, SubCategory: any) => {
				return (
					Category?.value === CHILD_IN_DIFFICULT_SITUATION &&
					age >= 21 &&
					(SubCategory?.value === CHILD_OF_PRISONER || SubCategory?.value === ORPHANS_ONLY)
				);
			},
			then: Yup.string().required().label("thisField").nullable(),
			otherwise: Yup.string().notRequired().nullable(),
		}),
		PursuingMilitaryService: Yup.string().when(["Category", "SubCategory"], {
			is: (Category: any, SubCategory: any) => {
				return (
					Category?.value === CHILD_IN_DIFFICULT_SITUATION &&
					age >= 21 &&
					(SubCategory?.value === CHILD_OF_PRISONER || SubCategory?.value === ORPHANS_ONLY)
				);
			},
			then: Yup.string().required().label("thisField").nullable(),
			otherwise: Yup.string().notRequired().nullable(),
		}),
		ReceivedLocalSupport: Yup.string().when("SubCategory", {
			is: (SubCategory) => {
				return SubCategory?.value === UNDER_45_AGE;
			},
			then: Yup.string().required().label("thisField").nullable(),
			otherwise: Yup.string().nullable(),
		}),
		MilitaryServiceStatus: Yup.object().when("SubCategory", {
			is: (SubCategory) => {
				return SubCategory?.value === UNDER_45_AGE;
			},
			then: Yup.object().shape({
				label: Yup.string(),
				value: Yup.string().required().label("thisField"),
			}),
			otherwise: Yup.object().notRequired(),
		}),
		Terminated: Yup.object().when("SubCategory", {
			is: (SubCategory) => {
				return SubCategory?.value === UNDER_45_AGE;
			},
			then: Yup.object().shape({
				label: Yup.string(),
				value: Yup.string().required().label("thisField"),
			}),
			otherwise: Yup.object().notRequired(),
		}),
		GuardianEmiratesID: Yup.string().when("SubCategory", {
			is: (SubCategory) => {
				return SubCategory?.value === BORN_UNKNOWN_PARENTS;
			},
			then: Yup.string()
				.required()
				.label("thisField")
				.matches(EMIRATES_ID_REGEX, "uaeIDNumberError"),
			otherwise: Yup.string().notRequired(),
		}),
		// NumberOfChildren: Yup.number().when(["SubCategory", "ChildEligibilityforWomeninDifficulty"], {
		// 	is: (SubCategory, ChildEligibilityforWomeninDifficulty) => {
		// 		//maleAndMarried
		// 		return (
		// 			(SubCategory?.value === DIVORCED ||
		// 				SubCategory?.value === SPOUSE_INCAPACITATED_FOREIGNER ||
		// 				SubCategory?.value === ABANDONED) &&
		// 			ChildEligibilityforWomeninDifficulty.length > 0 &&
		// 			!ChildEligibilityforWomeninDifficulty.find((item) => item.value === "662410003")
		// 		);
		// 	},
		// 	then: Yup.number().min(1, "greaterThanZero")

		// 	.test({
		// 		name: "is-1-or-2-digit",
		// 		message: "PleaseEntera1or2-digit",
		// 		test: (value: any) => /^[0-9]{1,2}$/.test(value),
		// 	})
		// 	.typeError("ThisFieldShouldbeNumber").required(),
		// 	otherwise: Yup.number().notRequired().nullable(),
		// }),
		NumberOfChildren: Yup.number().when(["SubCategory", "ChildEligibilityforWomeninDifficulty"], {
			is: (SubCategory, ChildEligibilityforWomeninDifficulty) =>
				(SubCategory?.value === DIVORCED ||
					SubCategory?.value === SPOUSE_INCAPACITATED_FOREIGNER ||
					SubCategory?.value === ABANDONED) &&
				ChildEligibilityforWomeninDifficulty?.length > 0 &&
				!ChildEligibilityforWomeninDifficulty?.find((item) => item.value === "662410003"),
			then: Yup.number()
				.min(1, "PleaseEnterNumbergraterThanZero")
				.test({
					name: "greater-than-or-equals-to-child-eligibility",
					message: "Number of children must be greater than or equal to child eligibility length",
					async test(value, context) {
						const { parent } = context;
						const childEligibilityLength = parent.ChildEligibilityforWomeninDifficulty?.length || 0;

						if (value && value >= childEligibilityLength) {
							return true;
						} else {
							return await Promise.reject(
								new Yup.ValidationError("InvalidNumberOfChildren", null, "NumberOfChildren")
							);
						}
					},
				})
				.test({
					name: "is-1-or-2-digit",
					message: "PleaseEntera1or2-digit",
					test: (value: any) => /^[0-9]{1,2}$/.test(value),
				})
				.typeError("ThisFieldShouldbeNumber")
				.required("Number of children is required"),
			otherwise: Yup.number().notRequired().nullable(),
		}),
		NumberOfChildrenLessThan25: Yup.number().when(
			["SubCategory", "ChildEligibilityforWomeninDifficulty"],
			{
				is: (SubCategory, ChildEligibilityforWomeninDifficulty) => {
					//maleAndMarried
					return (
						(SubCategory?.value === DIVORCED ||
							SubCategory?.value === SPOUSE_INCAPACITATED_FOREIGNER ||
							SubCategory?.value === ABANDONED) &&
						ChildEligibilityforWomeninDifficulty.length > 0 &&
						!ChildEligibilityforWomeninDifficulty.find((item) => item.value === "662410003") &&
						ChildEligibilityforWomeninDifficulty.find((item) => item.value === "662410002")
					);
				},
				then: Yup.number()
					.typeError("ThisFieldShouldbeNumber")
					.test({
						name: "is-1-or-2-digit",
						message: "PleaseEntera1or2-digit",
						test: (value: any) => /^[0-9]{1,2}$/.test(value),
					})
					.max(Yup.ref("numberOfChildren"), "NumberofPoDChildren")
					.min(1, "greaterThanZero")
					.max(Yup.ref("NumberOfChildren"), "thisFieldshouldbeLess")
					.required(),
				otherwise: Yup.number().notRequired().nullable(),
			}
		),
		DateOfTermination: Yup.date().when("Terminated", {
			is: (Terminated) => {
				return Terminated?.value === "1";
			},
			then: Yup.date().required().label("thisField").typeError("mustBeDate"),
			otherwise: Yup.date().notRequired().nullable(),
		}),
		// Release Prisoner specific field validations
		DateOfRelease: Yup.date().when("SubCategory", {
			is: (SubCategory) => {
				return SubCategory?.value === RELEASE_PRISONER;
			},
			then: Yup.date()
				.required()
				.label("thisField")
				.typeError("mustBeDate")
				.max(new Date(), "dateCannotBeInFuture"),
			otherwise: Yup.date().notRequired().nullable(),
		}),
		PrisonEntryDate: Yup.date().when("SubCategory", {
			is: (SubCategory) => {
				return SubCategory?.value === RELEASE_PRISONER;
			},
			then: Yup.date()
				.required()
				.label("thisField")
				.typeError("mustBeDate")
				.max(new Date(), "dateCannotBeInFuture"),
			otherwise: Yup.date().notRequired().nullable(),
		}),
		MilitaryServiceStatusForReleasePrisoner: Yup.object().when("SubCategory", {
			is: (SubCategory) => {
				return SubCategory?.value === RELEASE_PRISONER;
			},
			then: Yup.object().shape({
				label: Yup.string(),
				value: Yup.string().required().label("thisField"),
			}).required(),
			otherwise: Yup.object().notRequired().nullable(),
		}),
		// Rehabilitation Center specific field validations
		RehabCenter: Yup.object().when("SubCategory", {
			is: (SubCategory) => {
				return SubCategory?.value === ENROLLED_IN_REHAB_CENTER;
			},
			then: Yup.object().shape({
				label: Yup.string(),
				value: Yup.string().required().label("thisField"),
			}).required(),
			otherwise: Yup.object().notRequired().nullable(),
		}),
		TreatmentPlanStartDate: Yup.date().when("SubCategory", {
			is: (SubCategory) => {
				return SubCategory?.value === ENROLLED_IN_REHAB_CENTER;
			},
			then: Yup.date()
				.required()
				.label("thisField")
				.typeError("mustBeDate"),
			otherwise: Yup.date().notRequired().nullable(),
		}),
		TreatmentPlanEndDate: Yup.date().when(["SubCategory", "TreatmentPlanStartDate"], {
			is: (SubCategory, TreatmentPlanStartDate) => {
				return SubCategory?.value === ENROLLED_IN_REHAB_CENTER;
			},
			then: Yup.date()
				.required()
				.label("thisField")
				.typeError("mustBeDate")
				.min(Yup.ref("TreatmentPlanStartDate"), "endDateMustBeAfterStartDate"),
			otherwise: Yup.date().notRequired().nullable(),
		}),
	});
};

const onChange = (event: any, formikProps: FormikHelpers<any>) => {
	//console.log("Formik Validation", event, formikProps);
};
export { getInitialValues, onChange, getValidationSchema };
